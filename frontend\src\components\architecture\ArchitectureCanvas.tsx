import React, { use<PERSON><PERSON>back, useRef, useState, useMemo, useEffect } from 'react'
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  Connection,
  ConnectionMode,
  ReactFlowProvider,
  ReactFlowInstance,
  OnConnect,
  OnNodesChange,
  OnEdgesChange,
  BackgroundVariant,
  useReactFlow
} from 'reactflow'
import 'reactflow/dist/style.css'
import './ArchitectureCanvas.css'

import { ServiceNode, AWSServiceNode, GCPServiceNode, AzureServiceNode } from './nodes/ServiceNode'
import { UserNode } from './nodes/UserNode'
import { CloudService, ArchitectureNode, ArchitectureEdge } from '@/types/architecture'
import { getServiceById } from './utils/serviceDefinitions'
import { useToast } from '@/hooks/use-toast'

// Node types for React Flow
const nodeTypes = {
  awsService: AWSServiceNode,
  gcpService: GCPServiceNode,
  azureService: AzureServiceNode,
  serviceNode: ServiceNode,
  userNode: UserNode
}

// Edge types for React Flow
const edgeTypes = {
  // We can add custom edge types here later
}

interface ArchitectureCanvasProps {
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
  onNodesChange: OnNodesChange
  onEdgesChange: OnEdgesChange
  onConnect: OnConnect
  onNodeSelect: (node: ArchitectureNode | null) => void
  onAddNode: (service: CloudService, position: { x: number; y: number }) => void
  className?: string
}

export const ArchitectureCanvas: React.FC<ArchitectureCanvasProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onNodeSelect,
  onAddNode,
  className
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null)
  const { toast } = useToast()

  // Add keyboard event handling for edge deletion
  // Usage: Click on an edge to select it (it will turn red), then press Delete or Backspace to remove it
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Check if Delete or Backspace key is pressed
      if (event.key === 'Delete' || event.key === 'Backspace') {
        // Find selected edges
        const selectedEdges = edges.filter(edge => edge.selected)

        if (selectedEdges.length > 0) {
          // Prevent default browser behavior
          event.preventDefault()

          console.log(`Deleting ${selectedEdges.length} selected edge(s):`, selectedEdges.map(e => e.id))

          // Create edge removal changes
          const edgeChanges = selectedEdges.map(edge => ({
            id: edge.id,
            type: 'remove' as const
          }))

          // Apply the changes through onEdgesChange
          onEdgesChange(edgeChanges)

          toast({
            title: "Edges Deleted",
            description: `${selectedEdges.length} connection(s) removed from architecture.`,
          })
        }
      }
    }

    // Add event listener to document
    document.addEventListener('keydown', handleKeyDown)

    // Cleanup event listener on unmount
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [edges, onEdgesChange, toast])

  // Debug logging
  console.log(`ArchitectureCanvas: Rendering with ${nodes.length} nodes and ${edges.length} edges`)

  // Convert our architecture nodes to React Flow nodes
  const reactFlowNodes: Node[] = useMemo(() => {
    return nodes.map(node => {
      // Validate node type exists in nodeTypes
      const nodeType = node.type in nodeTypes ? node.type : 'awsService'

      return {
        id: node.id,
        type: nodeType,
        position: node.position,
        data: node.data,
        selected: node.selected || false,
        dragging: node.dragging || false,
        // Ensure all required React Flow properties are present with defaults
        width: node.width || 200,
        height: node.height || 100,
        zIndex: node.zIndex || 0,
        hidden: node.hidden || false,
        deletable: node.deletable !== false,
        selectable: node.selectable !== false,
        connectable: node.connectable !== false,
        focusable: node.focusable !== false
      }
    })
  }, [nodes])

  // Convert our architecture edges to React Flow edges
  const reactFlowEdges: Edge[] = useMemo(() => {
    console.log(`ArchitectureCanvas: Converting ${edges.length} edges to React Flow format`)

    const convertedEdges = edges.map(edge => {
      console.log(`Converting edge: ${edge.id} from ${edge.source} to ${edge.target}`)

      return {
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: edge.type || 'step',
        animated: edge.animated || false,
        style: edge.style || {
          stroke: edge.selected ? '#EF4444' : '#4F46E5', // Red when selected, blue otherwise
          strokeWidth: edge.selected ? 3.5 : 2.5, // Thicker when selected
          strokeDasharray: '0'
        },
        markerEnd: {
          type: 'arrowclosed',
          color: edge.selected ? '#EF4444' : '#4F46E5', // Red when selected, blue otherwise
          width: edge.selected ? 22 : 20, // Slightly larger when selected
          height: edge.selected ? 22 : 20
        },
        // Ensure all required React Flow properties are present with defaults
        sourceHandle: edge.sourceHandle || null,
        targetHandle: edge.targetHandle || null,
        selected: edge.selected || false,
        hidden: edge.hidden || false,
        deletable: edge.deletable !== false,
        selectable: edge.selectable !== false,
        focusable: edge.focusable !== false
      }
    })

    console.log(`ArchitectureCanvas: Converted ${convertedEdges.length} edges:`, convertedEdges)
    return convertedEdges
  }, [edges])

  const onDragOver = useCallback((event: React.DragEvent) => {
    console.log('🎯 Drag over canvas detected')
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      console.log('🎯 Drop event received on canvas!')
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()

      // Try both data formats for compatibility
      let serviceData = event.dataTransfer.getData('application/json')
      let service: CloudService | null = null

      if (!serviceData) {
        // Fallback to old format
        serviceData = event.dataTransfer.getData('application/reactflow')
      }

      if (!serviceData || !reactFlowBounds || !reactFlowInstance) {
        console.log('Drop failed: missing data or bounds', { serviceData: !!serviceData, reactFlowBounds: !!reactFlowBounds, reactFlowInstance: !!reactFlowInstance })
        return
      }

      try {
        const parsedData = JSON.parse(serviceData)

        // Handle new format with type and service properties
        if (parsedData.type === 'aws-service' && parsedData.service) {
          service = parsedData.service
        } else {
          // Handle old format (direct service object)
          service = parsedData
        }

        if (!service) {
          throw new Error('No service data found')
        }

        const position = reactFlowInstance.project({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        })

        console.log('Adding service to canvas:', service.name, 'at position:', position)
        onAddNode(service, position)

        toast({
          title: "Component Added",
          description: `${service.name} has been added to your architecture.`,
        })
      } catch (error) {
        console.error('Error parsing dropped service:', error, 'Raw data:', serviceData)
        toast({
          title: "Error",
          description: "Failed to add component to canvas.",
          variant: "destructive"
        })
      }
    },
    [reactFlowInstance, onAddNode, toast]
  )

  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      const architectureNode = nodes.find(n => n.id === node.id)
      onNodeSelect(architectureNode || null)
    },
    [nodes, onNodeSelect]
  )

  const onPaneClick = useCallback(() => {
    onNodeSelect(null)
  }, [onNodeSelect])

  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    console.log('Edge clicked:', edge.id)
    // Edge selection is handled automatically by React Flow
    // We can add additional logic here if needed
  }, [])

  const onInit = useCallback((instance: ReactFlowInstance) => {
    setReactFlowInstance(instance)
  }, [])

  return (
    <div className={className} ref={reactFlowWrapper}>
      <ReactFlow
        nodes={reactFlowNodes}
        edges={reactFlowEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={onInit}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onEdgeClick={onEdgeClick}
        onPaneClick={onPaneClick}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        connectionMode={ConnectionMode.Strict}
        defaultEdgeOptions={{
          type: 'step',
          markerEnd: {
            type: 'arrowclosed',
            color: '#4F46E5',
            width: 20,
            height: 20
          },
          style: {
            stroke: '#4F46E5',
            strokeWidth: 2.5,
            strokeDasharray: '0'
          }
        }}
        fitView
        fitViewOptions={{
          padding: 0.2,
          includeHiddenNodes: false,
          minZoom: 0.2,
          maxZoom: 2.0
        }}
        minZoom={0.2}
        maxZoom={2.0}
        defaultZoom={0.5}
        defaultViewport={{ x: 0, y: 0, zoom: 0.5 }}
        attributionPosition="bottom-left"
      >
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={2}
          color="#e5e5e5"
        />
        <Controls
          position="top-left"
          showZoom={true}
          showFitView={true}
          showInteractive={true}
        />
        <MiniMap
          position="bottom-right"
          nodeColor={(node) => {
            const architectureNode = nodes.find(n => n.id === node.id)
            if (architectureNode?.data.service.provider === 'AWS') return '#FF9900'
            if (architectureNode?.data.service.provider === 'GCP') return '#4285F4'
            if (architectureNode?.data.service.provider === 'Azure') return '#0078D4'
            return '#6b7280'
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            border: '1px solid #e5e5e5',
            borderRadius: '8px'
          }}
        />
      </ReactFlow>
    </div>
  )
}

// Wrapper component with ReactFlowProvider
export const ArchitectureCanvasWrapper: React.FC<ArchitectureCanvasProps> = (props) => {
  return (
    <ReactFlowProvider>
      <ArchitectureCanvas {...props} />
    </ReactFlowProvider>
  )
}

export default ArchitectureCanvasWrapper
